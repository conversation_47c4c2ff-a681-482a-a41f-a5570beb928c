import type { ShaderMaterial } from "three";
import GUI from "lil-gui";

import { DEFAULT_CONFIG, MaterialGlobalProcess } from "./MaterialGlobalProcess";


import type { ImageProcessParams } from './MaterialGlobalProcess';
export class GlobalProcessCtrl {
    private gui: GUI | null = null;
    private materials: ShaderMaterial[] = [];
    private params: ImageProcessParams = {
        temperature: DEFAULT_CONFIG.temperature.default,
        contrast: DEFAULT_CONFIG.contrast.default,
        brightness: DEFAULT_CONFIG.brightness.default,
        saturation: DEFAULT_CONFIG.saturation.default,
        gamma: DEFAULT_CONFIG.gamma.default
    };

    /**
     * 初始化 GUI 控制面板
     * @param materials 要控制的着色器材质数组
     * @param container 容器元素，可选
     */
    public initGUI(materials: ShaderMaterial[], container?: HTMLElement): void {
        this.materials = materials;

        // 销毁现有的 GUI
        if (this.gui) {
            this.gui.destroy();
        }

        // 创建新的 GUI
        this.gui = new GUI({ container });
        this.gui.title('图像调整');

        let params = this.gui.addFolder('参数');

        // 色温控制
        params.add(this.params, 'temperature', DEFAULT_CONFIG.temperature.min, DEFAULT_CONFIG.temperature.max, 0.01)
            .name('色温')
            .onChange((value: number) => {
                this.updateMaterialParam('temperature', value);
            });

        // 对比度控制
        params.add(this.params, 'contrast', DEFAULT_CONFIG.contrast.min, DEFAULT_CONFIG.contrast.max, 0.01)
            .name('对比度')
            .onChange((value: number) => {
                this.updateMaterialParam('contrast', value);
            });

        // 亮度控制
        params.add(this.params, 'brightness', DEFAULT_CONFIG.brightness.min, DEFAULT_CONFIG.brightness.max, 0.01)
            .name('亮度')
            .onChange((value: number) => {
                this.updateMaterialParam('brightness', value);
            });

        // 饱和度控制
        params.add(this.params, 'saturation', DEFAULT_CONFIG.saturation.min, DEFAULT_CONFIG.saturation.max, 0.01)
            .name('饱和度')
            .onChange((value: number) => {
                this.updateMaterialParam('saturation', value);
            });

        // 伽马校正控制
        params.add(this.params, 'gamma', DEFAULT_CONFIG.gamma.min, DEFAULT_CONFIG.gamma.max, 0.01)
            .name('伽马校正')
            .onChange((value: number) => {
                this.updateMaterialParam('gamma', value);
            });

        const updateParamCtrl = () => {
            params.controllers.forEach(controller => {
                controller.updateDisplay();
            });
        }

        // 添加重置按钮
        this.gui.add({
            reset: () => {
                this.resetToDefaults();
                updateParamCtrl();
            }
        }, 'reset').name('重置为默认值');

        // 添加预设按钮
        const presets = this.gui.addFolder('预设');
        presets.add({
            warm: () => {
                this.applyPreset({
                    temperature: 0.3,
                    contrast: 1.1,
                    brightness: 0.1,
                    saturation: 1.2,
                    gamma: 1.0
                });
                updateParamCtrl();
            }
        }, 'warm').name('暖色调');

        presets.add({
            cool: () => {
                this.applyPreset({
                    temperature: -0.3,
                    contrast: 1.2,
                    brightness: -0.1,
                    saturation: 0.9,
                    gamma: 1.1
                });
                updateParamCtrl();
            }
        }, 'cool').name('冷色调');

        presets.add({
            vibrant: () => {
                this.applyPreset({
                    temperature: 0.0,
                    contrast: 1.3,
                    brightness: 0.2,
                    saturation: 1.5,
                    gamma: 0.9
                });
                updateParamCtrl();
            }
        }, 'vibrant').name('鲜艳');

        presets.add({
            vintage: () => {
                this.applyPreset({
                    temperature: 0.2,
                    contrast: 0.8,
                    brightness: -0.1,
                    saturation: 0.7,
                    gamma: 1.2
                });
                updateParamCtrl();
            }
        }, 'vintage').name('复古');
    }

    /**
     * 更新所有材质参数
     * @param paramName 参数名称
     * @param value 参数值
     */
    private updateMaterialParam(paramName: keyof ImageProcessParams, value: number): void {
        if (this.materials.length > 0) {
            const updateParams: Partial<ImageProcessParams> = {};
            updateParams[paramName] = value;
            // 更新所有材质
            this.materials.forEach((material) => {
                MaterialGlobalProcess.updateParams(material, updateParams);
            });
        }
    }

    /**
     * 重置为默认值 - 使用配置中的默认值
     */
    private resetToDefaults(): void {
        const defaults: ImageProcessParams = {
            temperature: DEFAULT_CONFIG.temperature.default,
            contrast: DEFAULT_CONFIG.contrast.default,
            brightness: DEFAULT_CONFIG.brightness.default,
            saturation: DEFAULT_CONFIG.saturation.default,
            gamma: DEFAULT_CONFIG.gamma.default
        };
        this.applyPreset(defaults);
    }

    /**
     * 应用预设参数
     * @param preset 预设参数
     */
    private applyPreset(preset: ImageProcessParams): void {
        // 更新内部参数 - 逐个更新以确保 lil-gui 能检测到变化
        if (preset.temperature !== undefined) {
            this.params.temperature = preset.temperature;
        }
        if (preset.contrast !== undefined) {
            this.params.contrast = preset.contrast;
        }
        if (preset.brightness !== undefined) {
            this.params.brightness = preset.brightness;
        }
        if (preset.saturation !== undefined) {
            this.params.saturation = preset.saturation;
        }
        if (preset.gamma !== undefined) {
            this.params.gamma = preset.gamma;
        }

        // 更新所有材质
        if (this.materials.length > 0) {
            this.materials.forEach(material => {
                MaterialGlobalProcess.updateParams(material, preset);
            });
        }
    }

    /**
     * 获取当前参数
     * @returns 当前图像处理参数
     */
    public getParams(): ImageProcessParams {
        return { ...this.params };
    }

    /**
     * 设置参数
     * @param params 新的参数
     */
    public setParams(params: ImageProcessParams): void {
        Object.assign(this.params, params);
        if (this.materials.length > 0) {
            this.materials.forEach(material => {
                MaterialGlobalProcess.updateParams(material, params);
            });
        }
    }

    /**
     * 销毁 GUI
     */
    public destroy(): void {
        if (this.gui) {
            this.gui.destroy();
            this.gui = null;
        }
        this.materials = [];
    }

    /**
     * 显示/隐藏 GUI
     * @param visible 是否显示
     */
    public setVisible(visible: boolean): void {
        if (this.gui) {
            this.gui.domElement.style.display = visible ? 'block' : 'none';
        }
    }

    /**
     * 获取当前控制的材质数量
     * @returns 材质数量
     */
    public getMaterialCount(): number {
        return this.materials.length;
    }

    /**
     * 添加材质到控制列表
     * @param material 要添加的材质
     */
    public addMaterial(material: ShaderMaterial): void {
        if (!this.materials.includes(material)) {
            this.materials.push(material);
        }
    }

    /**
     * 从控制列表中移除材质
     * @param material 要移除的材质
     */
    public removeMaterial(material: ShaderMaterial): void {
        const index = this.materials.indexOf(material);
        if (index > -1) {
            this.materials.splice(index, 1);
        }
    }
}