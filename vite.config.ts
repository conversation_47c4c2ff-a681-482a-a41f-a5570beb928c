import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import fs from 'fs'
import path from 'path'


// https://vite.dev/config/
export default defineConfig({
  plugins: [react()],
  server: {
    host: 'dev-pre-pano.3vjia.com',
    port: 8086,
    https: {
      key: fs.readFileSync(path.resolve(__dirname, 'cert/private-key.pem')),
      cert: fs.readFileSync(path.resolve(__dirname, 'cert/certificate.pem')),
    },
  },
})
