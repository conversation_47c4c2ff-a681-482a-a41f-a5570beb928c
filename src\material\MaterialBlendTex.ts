import { BackSide, ShaderMaterial, Texture } from "three";

export class MaterialBlendTex {
    // 私有静态属性：自定义着色器材质 - 基于 MeshBasicMaterial 的简化版本，支持多张光照贴图同时使用
    private static readonly CustomPanoramaMaterial = {
        vertexShader: `
        varying vec2 vUv;
        varying vec3 vNormal;
        varying vec3 vPosition;
        
        void main() {
          vUv = uv;
          vNormal = normalize(normalMatrix * normal);
          vPosition = position;
          gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
        }
      `,

        fragmentShader: `
        uniform sampler2D map;
        uniform sampler2D lightMap1;
        uniform sampler2D lightMap2;
        uniform sampler2D lightMap3;
        uniform sampler2D lightMap4;
        uniform float lightMapWeight1;
        uniform float lightMapWeight2;
        uniform float lightMapWeight3;
        uniform float lightMapWeight4;
        uniform float lightMapIntensity;
        varying vec2 vUv;
        varying vec3 vNormal;
        varying vec3 vPosition;
        
        void main() {
          vec4 texColor = texture2D(map, vUv);
          
          // 采样所有光照贴图
          vec4 lightMapColor1 = texture2D(lightMap1, vUv);
          vec4 lightMapColor2 = texture2D(lightMap2, vUv);
          vec4 lightMapColor3 = texture2D(lightMap3, vUv);
          vec4 lightMapColor4 = texture2D(lightMap4, vUv);
          
          // 按权重混合所有光照贴图
          vec4 combinedLightMap = lightMapColor1 * lightMapWeight1 +
                                  lightMapColor2 * lightMapWeight2 +
                                  lightMapColor3 * lightMapWeight3 +
                                  lightMapColor4 * lightMapWeight4;
          
          // 混合基础纹理和光照贴图
          vec4 finalColor = mix(texColor, combinedLightMap, lightMapIntensity);
          
          gl_FragColor = finalColor;
        }
      `
    };

    /**
     * 创建支持多张光照贴图混合的材质
     * @param texture 基础纹理
     * @param lightMapTextures 光照贴图纹理数组
     * @param lightMapWeights 光照贴图权重数组
     * @param lightMapIntensity 光照贴图强度
     * @returns 自定义着色器材质
     */
    public static createMaterial(
        texture: Texture,
        lightMapTextures: Texture[] = [],
        lightMapWeights: number[] = [],
        lightMapIntensity: number = 0
    ): ShaderMaterial {
        const uniforms: any = {
            map: { value: texture },
            lightMapIntensity: { value: lightMapIntensity },
            lightMap1: { value: null },
            lightMap2: { value: null },
            lightMap3: { value: null },
            lightMap4: { value: null },
            lightMapWeight1: { value: 0.0 },
            lightMapWeight2: { value: 0.0 },
            lightMapWeight3: { value: 0.0 },
            lightMapWeight4: { value: 0.0 }
        };

        // 设置光照贴图纹理和权重
        for (let i = 0; i < Math.min(4, lightMapTextures.length); i++) {
            uniforms[`lightMap${i + 1}`] = { value: lightMapTextures[i] };
            uniforms[`lightMapWeight${i + 1}`] = { value: lightMapWeights[i] || 1.0 };
        }

        const customMaterial = new ShaderMaterial({
            vertexShader: MaterialBlendTex.CustomPanoramaMaterial.vertexShader,
            fragmentShader: MaterialBlendTex.CustomPanoramaMaterial.fragmentShader,
            uniforms: uniforms,
            side: BackSide
        });

        return customMaterial;
    }

    /**
     * 设置光照贴图权重
     * @param material 着色器材质
     * @param weights 权重数组
     */
    public static setLightMapWeights(material: ShaderMaterial, weights: number[]): void {
        for (let i = 0; i < Math.min(4, weights.length); i++) {
            const uniformName = `lightMapWeight${i + 1}`;
            if (material.uniforms[uniformName]) {
                material.uniforms[uniformName].value = weights[i] || 0.0;
            }
        }
    }

    /**
     * 设置光照贴图强度
     * @param material 着色器材质
     * @param intensity 强度值 (0-1)
     */
    public static setLightMapIntensity(material: ShaderMaterial, intensity: number): void {
        if (material.uniforms.lightMapIntensity) {
            material.uniforms.lightMapIntensity.value = Math.max(0, Math.min(1, intensity));
        }
    }

    /**
     * 更新光照贴图纹理
     * @param material 着色器材质
     * @param index 贴图索引 (0-3)
     * @param texture 新的纹理
     */
    public static updateLightMapTexture(material: ShaderMaterial, index: number, texture: Texture): void {
        const uniformName = `lightMap${index + 1}`;
        if (material.uniforms[uniformName] && index >= 0 && index < 4) {
            material.uniforms[uniformName].value = texture;
        }
    }
}