import { BackSide, ShaderMaterial, Texture } from "three";

// 参数配置接口
export interface ParameterConfig {
    min: number; // 最小值
    max: number; // 最大值
    default: number; // 默认值
}

// 图像处理配置接口
export interface ImageProcessConfig {
    temperature: ParameterConfig; // 色温
    contrast: ParameterConfig; // 对比度
    brightness: ParameterConfig; // 亮度
    saturation: ParameterConfig; // 饱和度
    gamma: ParameterConfig; // 伽马
}

// 图像处理参数接口
export interface ImageProcessParams {
    temperature?: number; // 色温
    contrast?: number; // 对比度
    brightness?: number; // 亮度
    saturation?: number; // 饱和度
    gamma?: number; // 伽马
}

// 默认配置
export const DEFAULT_CONFIG: ImageProcessConfig = {
    temperature: { min: -10, max: 10, default: 0.0 }, // 色温
    contrast: { min: 0.5, max: 1.5, default: 1.0 }, // 对比度
    brightness: { min: -1, max: 1, default: 0.0 }, // 亮度
    saturation: { min: 0.0, max: 2.0, default: 1.0 }, // 饱和度
    gamma: { min: 0, max: 2.0, default: 1.0 } // 伽马
};

/**
* @description 全局图像处理材质，支持:
* 色温，0 原色，正数偏暖（偏黄），负数偏冷（偏蓝）
* 对比度，1 原色，大于1偏亮，小于1偏暗，公式：out = (in - 128) × contrast + 128
* 亮度，0 原色，正数偏亮， 负数偏暗，公式：out = in + brightness
* 饱和度，1 原色，正数偏饱和，负数偏灰，调整 HSV 空间的 S 通道， 公式：out = in * saturation
* 伽马校正，1 原色，正数偏亮，负数偏暗，公式：out = in ^ (1 / gamma)
* <AUTHOR>
* @date 2025-07-15 14:51:33
* @lastEditTime 2025-07-15 14:51:33
* @lastEditors xuld
*/

export class MaterialGlobalProcess {
    // 工具函数：限制值在指定范围内
    private static between(value: number, min: number, max: number): number {
        return Math.max(min, Math.min(max, value));
    }

    // 工具函数：获取参数值（应用默认值和范围限制）
    private static getParameterValue(value: number | undefined, config: ParameterConfig): number {
        const defaultValue = value ?? config.default;
        return this.between(defaultValue, config.min, config.max);
    }

    // 工具函数：合并配置
    private static mergeConfig(customConfig?: Partial<ImageProcessConfig>): ImageProcessConfig {
        if (!customConfig) {
            return DEFAULT_CONFIG;
        }

        return {
            temperature: { ...DEFAULT_CONFIG.temperature, ...customConfig.temperature },
            contrast: { ...DEFAULT_CONFIG.contrast, ...customConfig.contrast },
            brightness: { ...DEFAULT_CONFIG.brightness, ...customConfig.brightness },
            saturation: { ...DEFAULT_CONFIG.saturation, ...customConfig.saturation },
            gamma: { ...DEFAULT_CONFIG.gamma, ...customConfig.gamma }
        };
    }

    // 私有静态属性：自定义着色器材质 - 支持图像处理调整
    private static readonly CustomPanoramaMaterial = {
        vertexShader: `
        varying vec2 vUv;
        
        void main() {
          vUv = uv;
          gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
        }
      `,

        fragmentShader: `
        uniform sampler2D map;
        varying vec2 vUv;

        // 图像处理参数
        uniform float temperature;
        uniform float contrast;
        uniform float brightness;
        uniform float saturation;
        uniform float gamma;
        
        // RGB转HSV
        vec3 rgb2hsv(vec3 c) {
            vec4 K = vec4(0.0, -1.0 / 3.0, 2.0 / 3.0, -1.0);
            vec4 p = mix(vec4(c.bg, K.wz), vec4(c.gb, K.xy), step(c.b, c.g));
            vec4 q = mix(vec4(p.xyw, c.r), vec4(c.r, p.yzx), step(p.x, c.r));
            
            float d = q.x - min(q.w, q.y);
            float e = 1.0e-10;
            return vec3(abs(q.z + (q.w - q.y) / (6.0 * d + e)), d / (q.x + e), q.x);
        }
        
        // HSV转RGB
        vec3 hsv2rgb(vec3 c) {
            vec4 K = vec4(1.0, 2.0 / 3.0, 1.0 / 3.0, 3.0);
            vec3 p = abs(fract(c.xxx + K.xyz) * 6.0 - K.www);
            return c.z * mix(K.xxx, clamp(p - K.xxx, 0.0, 1.0), c.y);
        }
        
        // 色温调整矩阵
        mat3 getTemperatureMatrix(float temp) {
            if (temp > 0.0) {
                // 暖色调 - 增强效果
                return mat3(
                    1.0 + temp * 0.3, 0.0, 0.0,  // 增强红色
                    0.0, 1.0 + temp * 0.15, 0.0, // 增强绿色
                    0.0, 0.0, 1.0 - temp * 0.4   // 减少蓝色
                );
            } else {
                // 冷色调 - 增强效果
                return mat3(
                    1.0 + temp * 0.4, 0.0, 0.0,  // 减少红色
                    0.0, 1.0 + temp * 0.15, 0.0, // 减少绿色
                    0.0, 0.0, 1.0 - temp * 0.3   // 增强蓝色
                );
            }
        }
        
        void main() {
          vec4 texColor = texture(map, vUv);
          vec3 color = texColor.rgb;
          
          // 1. 色温调整
          mat3 tempMatrix = getTemperatureMatrix(temperature);
          color = tempMatrix * color;
          color = clamp(color, 0.0, 1.0);
          
          // 2. 对比度调整
          color = (color - 0.5) * contrast + 0.5;
          color = clamp(color, 0.0, 1.0);
          
          // 3. 亮度调整
          color += brightness;
          color = clamp(color, 0.0, 1.0);
          
          // 4. 饱和度调整
          vec3 hsv = rgb2hsv(color);
          hsv.y = clamp(hsv.y * saturation, 0.0, 1.0);
          color = hsv2rgb(hsv);
          color = clamp(color, 0.0, 1.0);
          
          // 5. 伽马校正
          color = pow(color, vec3(1.0 / gamma));
          color = clamp(color, 0.0, 1.0);
          
          gl_FragColor = vec4(color, texColor.a);
        }
      `
    };

    /**
     * 创建支持全局图像处理的材质
     * @param texture 纹理对象
     * @param params 图像处理参数
     * @param config 可选的参数配置（部分或全部覆盖默认配置）
     * @returns 自定义着色器材质
     */
    public static createMaterial(
        texture: Texture,
        params: ImageProcessParams = {},
        config?: Partial<ImageProcessConfig>
    ): ShaderMaterial {
        const mergedConfig = this.mergeConfig(config);

        // 使用配置获取参数值
        const temperature = this.getParameterValue(params.temperature, mergedConfig.temperature);
        const contrast = this.getParameterValue(params.contrast, mergedConfig.contrast);
        const brightness = this.getParameterValue(params.brightness, mergedConfig.brightness);
        const saturation = this.getParameterValue(params.saturation, mergedConfig.saturation);
        const gamma = this.getParameterValue(params.gamma, mergedConfig.gamma);

        const uniforms: any = {
            map: { value: texture },
            temperature: { value: temperature },
            contrast: { value: contrast },
            brightness: { value: brightness },
            saturation: { value: saturation },
            gamma: { value: gamma }
        };

        const customMaterial = new ShaderMaterial({
            vertexShader: MaterialGlobalProcess.CustomPanoramaMaterial.vertexShader,
            fragmentShader: MaterialGlobalProcess.CustomPanoramaMaterial.fragmentShader,
            uniforms: uniforms,
            side: BackSide
        });

        return customMaterial;
    }

    /**
     * 更新材质参数的辅助方法
     * @param material 着色器材质
     * @param params 图像处理参数
     * @param config 可选的参数配置（部分或全部覆盖默认配置）
     */
    public static updateParams(
        material: ShaderMaterial,
        params: ImageProcessParams,
        config?: Partial<ImageProcessConfig>
    ): void {
        const mergedConfig = this.mergeConfig(config);

        if (params.temperature !== undefined) {
            const clampedTemp = this.between(params.temperature, mergedConfig.temperature.min, mergedConfig.temperature.max);
            material.uniforms.temperature.value = clampedTemp;
        }
        if (params.contrast !== undefined) {
            const clampedContrast = this.between(params.contrast, mergedConfig.contrast.min, mergedConfig.contrast.max);
            material.uniforms.contrast.value = clampedContrast;
        }
        if (params.brightness !== undefined) {
            const clampedBrightness = this.between(params.brightness, mergedConfig.brightness.min, mergedConfig.brightness.max);
            material.uniforms.brightness.value = clampedBrightness;
        }
        if (params.saturation !== undefined) {
            const clampedSaturation = this.between(params.saturation, mergedConfig.saturation.min, mergedConfig.saturation.max);
            material.uniforms.saturation.value = clampedSaturation;
        }
        if (params.gamma !== undefined) {
            const clampedGamma = this.between(params.gamma, mergedConfig.gamma.min, mergedConfig.gamma.max);
            material.uniforms.gamma.value = clampedGamma;
        }

        // 强制材质更新
        material.needsUpdate = true;
    }
}