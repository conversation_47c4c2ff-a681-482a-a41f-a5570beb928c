import { useEffect, useRef } from "react";
import * as THREE from "three";
import { OrbitControls } from "three/addons/controls/OrbitControls.js";

import { CubeMaterialMgr } from "./material/CubeMaterialMgr";
import { GlobalProcessCtrl } from "./material/GlobalProcessCtrl";


function App() {
  // 0: MeshBasicMaterial
  // 1: 混合纹理材质
  // 2: 全局色器材质
  const materialMode = 2;

  const mountRef = useRef<HTMLDivElement>(null);
  const rendererRef = useRef<THREE.WebGLRenderer | null>(null);
  const cameraRef = useRef<THREE.PerspectiveCamera | null>(null);
  const globalProcessCtrl = new GlobalProcessCtrl();

  // 处理窗口尺寸变化的函数
  const handleResize = () => {
    const mount = mountRef.current;
    const renderer = rendererRef.current;
    const camera = cameraRef.current;

    if (!mount || !renderer || !camera) return;

    const width = mount.clientWidth;
    const height = mount.clientHeight;

    // 更新相机宽高比
    camera.aspect = width / height;
    camera.updateProjectionMatrix();

    // 更新渲染器尺寸
    renderer.setSize(width, height);
  };

  useEffect(() => {
    const mount = mountRef.current;
    if (!mount) return;

    const scene = new THREE.Scene();
    const camera = new THREE.PerspectiveCamera(75, mount.clientWidth / mount.clientHeight, 0.1, 1000);
    camera.position.set(0, 0, 0.1);
    const renderer = new THREE.WebGLRenderer();
    renderer.setSize(mount.clientWidth, mount.clientHeight);

    // 保存引用以便在resize时使用
    rendererRef.current = renderer;
    cameraRef.current = camera;

    // 设置色调映射来降低整体亮度
    // renderer.toneMapping = THREE.ACESFilmicToneMapping;
    // renderer.toneMappingExposure = 0.8; // 降低曝光度
    renderer.outputColorSpace = THREE.SRGBColorSpace;

    mount.appendChild(renderer.domElement);

    // 添加轨道控制器
    const controls = new OrbitControls(camera, renderer.domElement);
    controls.enableDamping = true;
    controls.dampingFactor = 0.05;
    controls.enableZoom = true;
    controls.enablePan = false;
    controls.minDistance = 0.1;
    controls.maxDistance = 20;

    // 监听窗口尺寸变化
    window.addEventListener('resize', handleResize);

    CubeMaterialMgr.getCubeMaterials(materialMode).then((materials: THREE.Material[]) => {
      let skyBox = new THREE.Mesh(new THREE.BoxGeometry(10, 10, 10), materials);
      skyBox.rotation.set(0, Math.PI, 0);
      scene.add(skyBox);

      // 初始化 GUI 控制面板（仅对全局处理材质）
      if (materialMode === 2) {
        globalProcessCtrl.initGUI(materials as THREE.ShaderMaterial[]); // 控制所有着色器材质
      }

      animate();
    });

    function animate() {
      requestAnimationFrame(animate);
      controls.update();
      renderer.render(scene, camera);
    }

    return () => {
      // 清理事件监听器
      window.removeEventListener('resize', handleResize);

      // 清理 GUI 控制面板
      if (materialMode === 2) {
        globalProcessCtrl.destroy();
      }

      // 清理Three.js资源
      mount.removeChild(renderer.domElement);
      renderer.dispose();

      // 清理引用
      rendererRef.current = null;
      cameraRef.current = null;
    };
  }, []);

  return <div ref={mountRef} style={{ width: '100vw', height: '100vh' }} />;
}

export default App;
